% digital notch filter (11/26)

clear all;

f_sample = 62500; %300;
f_notch = 15625; %60;

filter_type = 1; % 1: iirnotch, 2: butterworth bandstop

switch filter_type
    case 1
        q = 20;
        w0 = f_notch/(f_sample/2);
        bw = w0/q;
        [B, A] = iirnotch(w0, bw);

    case 2
        N = 10; % butter order
        Wn = [(f_notch-100) (f_notch+100)]/(f_sample/2);
        [B,A] = butter(N,Wn,'stop'); % is a bandstop filter if Wn = [W1 W2].
end

fvtool(B, A);

%[B, A] = butter(10, 0.4);
input = zeros(100,1);
input(1) = 1;
h = filter(B, A, input);
Mag = abs(freqz(B, A, 1000));
Ph = angle(freqz(B, A, 1000));
% x = sound1;
% x_lpf = filter(B, A, x);


input = randn(10000,1);
output = filter(B, A, input);
for i = 1:10000
    if i <= 3
        output2(i) = 0;
    else
        output2(i) = -A(2)*output2(i-1) -A(3)*output2(i-2) + B(1)*input(i) + B(2)*input(i-1) + B(3)*input(i-2);
    end
end
    
fft_input = 20*log10(abs(fft(input)));
fft_output = 20*log10(abs(fft(output)));
fft_output2 = 20*log10(abs(fft(output2)));

figure(410);
M1 = 5;
m1 = 1;
len1 = length(input);
kkk = 1:len1;
subplot(M1,1,m1); plot(kkk, input(kkk), '-'); title('input'); ylabel(''); grid; m1 = m1 + 1;
subplot(M1,1,m1); plot(kkk, fft_input(kkk), '-'); title('fft-input'); ylabel('db'); grid; m1 = m1 + 1;
subplot(M1,1,m1); plot(kkk, output(kkk), '-'); title('output'); ylabel(''); grid; m1 = m1 + 1;
subplot(M1,1,m1); plot(kkk, fft_output(kkk), '-'); title('fft-output'); ylabel('db'); grid; m1 = m1 + 1;
subplot(M1,1,m1); plot(kkk, fft_output2(kkk), '-'); title('fft-output2'); ylabel('db'); grid; m1 = m1 + 1;


figure(420);
M1 = 2;
m1 = 1;
Ph1 = unwrap(Ph)*180/pi; 
u = diff(Ph);
len1 = length(Ph);
kkk = 1:len1;
Ph2 = u(1)*kkk*180/pi; % ideal phase response (linear phase)
subplot(M1,1,m1); plot(kkk, Ph1(kkk), '-', kkk, Ph2(kkk), '--'); legend('iir', 'linear'); title('phase response'); ylabel('deg'); grid; m1 = m1 + 1;
subplot(M1,1,m1); plot(kkk, 20*log10(Mag(kkk)), '-'); title('mag response'); ylabel('db'); ylim([-100 0]); grid; m1 = m1 + 1; %ylim([-5e1 5e1]);

