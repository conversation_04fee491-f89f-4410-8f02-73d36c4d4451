% ===================== AIS 수신 고급 최적화 실험 (알고리즘 비교) =====================
clear; clc;

fprintf('=== AIS 수신 고급 최적화 실험: 알고리즘별 성능 비교 ===\n');

% 최적 파라미터 고정
params = struct('notch_freq', 45, 'BT', 0.4, 'MAX_SYNC_CORRVAL', 0.75, ...
    'ADC_SUB_DC_OFFSET', 1450, 'DC_AVG_COUNT', 60, 'MAX_SYNC_COUNT', 20);

% 데이터 로딩 및 공통 전처리
[data, fs] = load_and_preprocess(params);

% 알고리즘별 수신 함수 실행
pkt_cnt_legacy = ais_rx_legacy(data, params);

% 결과 비교 출력
fprintf('\n=== 알고리즘별 패킷 수 ===\n');
fprintf('기존 방식: %d\n', pkt_cnt_legacy);

% -------------------- 공통 데이터 전처리 함수 --------------------
function [data, fs] = load_and_preprocess(params)
    fs = 48000;
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch2.bin');
    raw = fread(G_hDumpFile, 'uint16');
    fclose(G_hDumpFile);
    % DC offset 보정 및 정규화
    data = (raw - params.ADC_SUB_DC_OFFSET) / 4095;
    % 노치 필터 적용
    wo = params.notch_freq/(fs/2); bw = wo/35;
    [b_notch, a_notch] = iirnotch(wo, bw);
    data = filter(b_notch, a_notch, data);
    % GMSK 필터 적용
    SPAN = 3; SPS = 4;
    h_gmsk = gaussdesign(params.BT, SPAN, SPS);
    data = conv(data, h_gmsk, 'same');
    % (선택) 정규화
    data = data / max(abs(data));
end

% -------------------- 기존 방식 수신 함수 --------------------
function pkt_cnt = ais_rx_legacy(data, params)
    % 기존 NRZI+DC 방식 (ais_rx_sim_advanced 구조 참고)
    pkt_cnt = ais_rx_sim_advanced(params.BT, params.MAX_SYNC_CORRVAL, params.notch_freq, ...
        params.ADC_SUB_DC_OFFSET, params.DC_AVG_COUNT, params.MAX_SYNC_COUNT, data);
end

% -------------------- 기존 방식 내부 함수 (수정) --------------------
function pkt_cnt = ais_rx_sim_advanced(BT, MAX_SYNC_CORRVAL, notch_freq, ADC_SUB_DC_OFFSET, DC_AVG_COUNT, MAX_SYNC_COUNT, data)
    % 고급 최적화된 AIS 수신 시뮬레이션 함수
    
    % 파라미터 초기화
    OSR = 5;
    ADC_MAX_VALUE = 4095;
    DC_MID_LEVEL = (1000 / 3300);
    DC_MAX_LEVEL = (1850 / 3300);
    DC_MIN_LEVEL = (0 / 3300);
    ENABLE_NOTCH_FLT = 1;
    ENABLE_GMSK_RX_FLT = 1;
    ENABLE_ADAPT_DC_OFFSET = 1;
    ENABLE_ADC_LIMIT = 2;
    ADC_MAX_ERROR_CNT = 30;
    DC_AVG_OFFSET = (OSR*5);
    DC_GAP = 0.000;
    START_DETECT_OFFSET = (OSR*8);
    SYNC_DETECT_OFFSET = (OSR*6)+2;
    RX_PLL_FULL = 2400;
    RX_PLL_HALF = (RX_PLL_FULL / 2);
    RX_PLL_INCR = (RX_PLL_FULL / OSR);
    RX_PLL_STEP = (RX_PLL_INCR / 3);
    RX_GMSK_TO_INT_FACTOR = 16;
    RX_GMSK_BT_0_5_FIR_N = 13;
    RX_MDM_STATUS_PREAMBLE = 0;
    RX_MDM_STATUS_START = 1;
    RX_MDM_STATUS_PRELOAD = 2;
    RX_MDM_STATUS_DATA = 3;
    RX_PRE_MAX_CNT_SIZE = 12;
    RX_PRE_MAX_BUF_SIZE = (RX_PRE_MAX_CNT_SIZE * OSR);
    
    % 노치 필터 생성
    fs = 48000;
    wo = notch_freq/(fs/2);
    bw = wo/35;
    [b_notch, a_notch] = iirnotch(wo, bw);
    NOTCH_FLT_B = b_notch;
    NOTCH_FLT_A = a_notch(2:3);
    
    % 데이터 로딩
    G_pSrcDataCh1 = data;
    
    % GMSK 필터 생성
    SPAN = 3; SPS = 4;
    impulse_response_of_gmsk = gaussdesign(BT, SPAN, SPS);
    RX_GMSK_BT_0_5_FIR_N = length(impulse_response_of_gmsk);
    
    % 프리앰블 생성
    dot_pattern = repmat([1, 1, 0, 0], 1, 6);
    preamble = dot_pattern';
    preamble_zero_padded = upsample(preamble, OSR);
    preamble_filtered_by_gmsk = conv(preamble_zero_padded, impulse_response_of_gmsk);
    
    % 변수 초기화
    data_length = length(G_pSrcDataCh1);
    G_pFilteredData = zeros(1, data_length);
    CorrelPreamble = zeros(1, data_length);
    AdativeDcOffset = zeros(1, data_length);
    BitArray = zeros(1, data_length);
    G_vRxRawDataBuff = zeros(1, RX_GMSK_BT_0_5_FIR_N);
    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
    G_wRxReferValue = DC_MID_LEVEL;
    G_dMaxSyncCorrel = 0;
    G_dMaxSyncCnt = 0;
    G_dSyncSymbolIndex = 0;
    G_PreStart = 0;
    G_PreOffset = 300;
    G_dSyncDetCnt = 0;
    G_dAdcErrCnt = 0;
    G_dStartErrCnt = 0;
    G_dPloadErrCnt = 0;
    G_dStuffErrCnt = 0;
    G_dCrcErrCnt = 0;
    G_dRcvPktCnt = 0;
    G_dStartSymbolIndex = 0;
    G_wRxShiftReg = 0;
    G_wRxBitCount = 0;
    G_wRxPrevBitD = 0;
    G_wRxNrziPrev = 0;
    G_wCrcRegData = 0;
    m_wRxMaxBitSize = 0;
    G_wRxNrziCntr = 0;
    G_dSwRxPllCntrX = 0;
    G_dSwRxPllSampP = 0;
    G_dSwRxPllSampC = 0;
    G_wRxCurrBitD = 0;
    G_dSwRxPllValue = 0;
    G_dRxAdcErrCnt = 0;
    G_wNewBitData = 0;
    G_bRxByteData = 0;
    G_BitDataArray = zeros(1, 500);
    
    % 필요한 테이블 데이터 추가
    G_vReverDataTableX = [...
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ...
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ...
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ...
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ...
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ...
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ...
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ...
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ...
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ...
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ...
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ...
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ...
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ...
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ...
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ...
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];
    
    G_vMaxBitSize = [...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];
    
    % 수신 루프
    for nCurSymbolIdx = 1:length(G_pSrcDataCh1)
        % Notch Filter
        if (ENABLE_NOTCH_FLT == 0)
            G_wRxAfAdcData = filter(NOTCH_FLT_B, [1 NOTCH_FLT_A], G_pSrcDataCh1(nCurSymbolIdx));
        else
            G_wRxAfAdcData = G_pSrcDataCh1(nCurSymbolIdx);
        end
        
        G_vRxRawDataBuff(1:1) = [];
        G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = G_wRxAfAdcData;
        
        % GMSK Filter
        G_pFilteredData(nCurSymbolIdx) = conv(G_vRxRawDataBuff, impulse_response_of_gmsk, 'valid');
        
        G_vGmskPreamble = preamble_filtered_by_gmsk;
        
        % Correlation Preamble
        if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
            if (nCurSymbolIdx <= length(G_vGmskPreamble))
                CorrelPreamble(nCurSymbolIdx) = 0;
            else
                tmp_100 = G_pSrcDataCh1(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nCurSymbolIdx) = (tmp_100)' * G_vGmskPreamble / norm(G_vGmskPreamble);
                
                if (CorrelPreamble(nCurSymbolIdx) > MAX_SYNC_CORRVAL && CorrelPreamble(nCurSymbolIdx) > G_dMaxSyncCorrel)
                    G_dMaxSyncCorrel = CorrelPreamble(nCurSymbolIdx);
                    G_dSyncSymbolIndex = nCurSymbolIdx;
                    G_dMaxSyncCnt = 0;
                elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                    G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
                end
                    
                if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL && G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                    range = (G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1:G_dSyncSymbolIndex-DC_AVG_OFFSET);
                    dc_sum = sum(G_pSrcDataCh1(range));
                    G_wRxReferValue = dc_sum / length(range) + DC_GAP;
                    
                    if (nCurSymbolIdx > G_PreOffset)
                        G_PreStart = nCurSymbolIdx-G_PreOffset;
                    else
                        G_PreStart = 1;
                    end
                    
                    G_dStartSymbolIndex = nCurSymbolIdx - START_DETECT_OFFSET;
                    G_dMaxSyncCorrel= 0;
                    G_dMaxSyncCnt   = 0;
                    G_wRxRunStatus  = RX_MDM_STATUS_START;
                    G_wRxShiftReg   = 0;
                    G_wRxBitCount   = 0;
                    G_wRxPrevBitD   = G_wRxNrziPrev;
                    G_dRxAdcErrCnt  = 0;
                    G_dSwRxPllValue = RX_PLL_HALF;
                    G_dSwRxPllCntrX = 1;
                    G_dSwRxPllSampC = G_wRxNrziPrev;
                    G_dSwRxPllSampP = G_wRxNrziPrev;
                    G_dSyncDetCnt = G_dSyncDetCnt + 1;
                end
            end
        end
        
        % ADC 제한 검사
        AdativeDcOffset(nCurSymbolIdx) = G_wRxReferValue;
        if (    (ENABLE_ADC_LIMIT == 1) && (G_pSrcDataCh1(nCurSymbolIdx) < DC_MIN_LEVEL || G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
            || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))
            if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
                G_dRxAdcErrCnt = 0;
                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;
            else
                G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
                if(G_dRxAdcErrCnt > ADC_MAX_ERROR_CNT)
                    G_dRxAdcErrCnt = 0;
                    G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                    G_wRxShiftReg  = 0;
                    G_wRxReferValue= DC_MID_LEVEL;
                    G_dAdcErrCnt = G_dAdcErrCnt+1;
                end
            end
        end
        
        % 상태머신 처리
        if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
            for idx = (G_dStartSymbolIndex : nCurSymbolIdx)
                if (ENABLE_GMSK_RX_FLT > 0)
                    G_wRxAfAdcData = G_pFilteredData(idx);
                else
                    G_wRxAfAdcData = G_pSrcDataCh1(idx);
                end
                
                G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
                G_dSwRxPllSampC = G_wRxNrziCurr;
                
                % PLL 처리
                if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                    if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
                        G_wRxNrziCntr = G_wRxNrziCntr + 1;
                        G_dSwRxPllSampC = G_dSwRxPllSampP;
                    else
                        G_wRxNrziCntr = 1;
                    end
                else
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                end
                
                if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                    if (G_wRxRunStatus == RX_MDM_STATUS_START)
                        if (G_dSwRxPllCntrX >= (OSR * 2 - 3) && G_dSwRxPllCntrX <= (OSR * 2 + 3))
                            G_dSwRxPllValue = RX_PLL_HALF;
                        end
                    end
                
                    if (G_dSwRxPllValue < RX_PLL_HALF)
                        G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
                    else
                        G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
                    end
                    
                    G_dSwRxPllCntrX = 1;
                else
                    G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
                end
                
                G_dSwRxPllSampP = G_dSwRxPllSampC;
                
                G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
                if(G_dSwRxPllValue >= RX_PLL_FULL)
                    G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
                else
                    continue;
                end
                
                G_wRxCurrBitD = G_dSwRxPllSampC;
                
                % 비트 처리
                G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
                if (G_wRxCurrBitD == G_wRxPrevBitD)
                    G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
                    BitArray(idx) = 0.05;
                else
                    G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
                    BitArray(idx) = 0.015;
                    
                    if (ENABLE_ADAPT_DC_OFFSET == 1)
                        if (ENABLE_GMSK_RX_FLT > 0)
                            sample = G_pFilteredData(idx);
                        else
                            sample = G_pSrcDataCh1(idx);
                        end
                        G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, 0.850);
                    end
                end
                
                G_wRxPrevBitD = G_wRxCurrBitD;
                
                % 상태머신 처리
                switch (G_wRxRunStatus)
                    case RX_MDM_STATUS_START
                        start_pattern_detected = false;
                        if (bitand(G_wRxShiftReg, 0x003f) == 0x003e || ...
                            bitand(G_wRxShiftReg, 0x001f) == 0x001e || ...
                            bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                            start_pattern_detected = true;
                        end
                        
                        if start_pattern_detected
                            G_wRxBitCount    = 0;
                            G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                        else
                            G_wRxBitCount = G_wRxBitCount + 1;
                            if(G_wRxBitCount >= 25)
                                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                                G_wRxShiftReg  = 0;
                                G_wRxReferValue= DC_MID_LEVEL;
                                G_dStartErrCnt = G_dStartErrCnt + 1;
                            end
                        end
                        
                    case RX_MDM_STATUS_PRELOAD
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if (G_wRxBitCount == 8)
                            G_wRxBitCount = 0;
                            G_wCrcRegData = 0xffff;
                            G_wRxRunStatus= RX_MDM_STATUS_DATA;
                            
                            nTemp = bitshift(G_wRxShiftReg, 2);
                            nTemp = bitand(nTemp, 0x00ff);
                            nMsgID = G_vReverDataTableX(nTemp + 1);
                            if (nMsgID < 0 || nMsgID > 27)
                                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                                G_wRxShiftReg  = 0;
                                G_wRxReferValue= DC_MID_LEVEL;
                                G_dPloadErrCnt = G_dPloadErrCnt + 1;
                            else
                                m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                            end
                        end
                        
                    case RX_MDM_STATUS_DATA
                        if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                            G_wRxBitCount = G_wRxBitCount + 1;
                            if(G_wRxBitCount >= m_wRxMaxBitSize+1)
                                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                                G_bRxByteData = 0;
                                G_wRxReferValue= DC_MID_LEVEL;
                                G_dStuffErrCnt = G_dStuffErrCnt + 1;
                                continue;
                            end
                            
                            G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                            G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                            G_BitDataArray(G_wRxBitCount) = G_wNewBitData;
                            
                            G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
                        end
                        
                        if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                            if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                                G_wRxShiftReg  = 0;
                                G_wRxReferValue= DC_MID_LEVEL;
                                G_bRxByteData = 0;
                                G_dRcvPktCnt = G_dRcvPktCnt + 1;
                            else
                                G_wRxBitCount    = 0;
                                G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                                G_bRxByteData    = 0;
                                G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                            end
                        end
                        
                    otherwise
                        warning('Unexpected run status.');
                end
            end
            G_dStartSymbolIndex = nCurSymbolIdx+1;
        end
    end
    pkt_cnt = G_dRcvPktCnt;
end

% Helper functions
function G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc)
    G_wRxReferValue = alpha_dc * G_wRxReferValue + (1-alpha_dc) * sample;
end

function bit = nrzi_decode(sample, refer)
    if sample > refer
        bit = 1;
    else
        bit = 0;
    end
end

function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end 