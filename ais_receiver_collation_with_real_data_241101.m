clear all;

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 0;                % 0: Ch1, 1: Ch2 Raw data
DETECT_PREAMBLE         = 1;                % 0: dot+start, 1: dot only
ENABLE_NOTCH_FLT        = 0;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 2;                % 0: Disable, 1: ORG GMSK Filter 2: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 2;                % 0: Disable, 1: MODE A, 2: Mode B, 3: Mode C Adaptive DC Offset
ENABLE_ADC_LIMIT        = 1;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable
%-------------------------------------------------------------------------
ENABLE_PLOT1            = 1;                % 0: Disable, 1: Sync detection (Matched filter)
ENABLE_PLOT2            = 0;                % 0: Disable, 1: Start detection
ENABLE_PLOT3            = 0;                % 0: Disable, 1: Received Data packet with CRC

ENABLE_PLOT96           = 0;                % 0: Disable, 1: ADC Max/Min Error
ENABLE_PLOT97           = 0;                % 0: Disable, 1: Start Bit Error
ENABLE_PLOT98           = 0;                % 0: Disable, 1: Stuffing Bit Error
ENABLE_PLOT99           = 0;                % 0: Disable, 1: CRC Error
%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

%-------------------------------------------------------------------------
 % Legacy modem defines
 NOTCH_FLT_A            = [+1.999986841577810, -0.999986910116283];
 NOTCH_FLT_B            = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
 % BT=0.5
 G_vRxGmskCoefficient   = [8, 187, 2427, 17821, 74016, 173924, 231226, 173924, 74016, 17821, 2427, 187, 8];

 DC_MIN_LEVEL           = floor(  20 * 4095 / 3300);    % 0.02V
 DC_MID_LEVEL           = floor(1000 * 4095 / 3300);    % 1.00V
 DC_MAX_LEVEL           = floor(2000 * 4095 / 3300);    % 2.00V

 RX_PLL_FULL            = 2400;
 RX_PLL_HALF            = (RX_PLL_FULL / 2);
 RX_PLL_INCR            = (RX_PLL_FULL / OSR);
 RX_PLL_STEP            = (RX_PLL_INCR / 3);

 RX_GMSK_BT_0_4_FIR_N   = 17;
 RX_GMSK_BT_0_5_FIR_N   = 13;
 RX_GMSK_TO_INT_FACTOR  = 16;

 RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

 RX_MDM_STATUS_PREAMBLE = 0;
 RX_MDM_STATUS_START    = 1;
 RX_MDM_STATUS_PRELOAD  = 2;
 RX_MDM_STATUS_DATA     = 3;

 RX_DOT_MAX_CNT_SIZE    = 7;
 RX_DOT_MAX_CNT_MASK    = 0x7f;
 RX_DOT_START_P_MASK    = 0x05;
 RX_DOT_DETCT_P_MASK    = 0x55;
 RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

 RX_PRE_MAX_CNT_SIZE    = 12;
 RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

 G_vNotchDataX = zeros(1, 3);

 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];

%-------------------------------------------------------------------------
dot_pattern             = repmat([1, 1, 0, 0], 1, 6);       % Dot pattern
start_pattern           = [0, 0, 0, 0, 0, 0, 0, 1];         % Start pattern
if (DETECT_PREAMBLE == 0)
    preamble            = [dot_pattern, start_pattern]';    % Preamble (dot + start pattern)
else
    preamble            = dot_pattern';                     % Preamble (dot pattern only)
end
preamble_os             = repelem(preamble, OSR);           % Over sampled preamble
LEN_DOT_PATTERN         = length(dot_pattern);              % Length of dot pattern
LEN_START_PATTERN       = length(start_pattern);            % Length of start pattern
LEN_PREAMBLE            = length(preamble);                 % Length of preamble
LEN_PREAMBLE_OS         = LEN_PREAMBLE*OSR;                 % Length of over sampled preamble
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% LENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

%-------------------------------------------------------------------------
% impulse response of gmsk filter
%-------------------------------------------------------------------------
SPAN = 4; SPS = 3;
impulse_response_of_gmsk            = gaussdesign(RX_BT, SPAN, SPS);
%[impulse_response_of_gmsk, len]    = gmsk_impulse_response(RX_BT, OSR, LEN_PSF, H_NORM);
impulse_response_of_gmsk_twice      = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);
RX_GMSK_BT_0_5_FIR_N                = length(impulse_response_of_gmsk);

preamble_zero_padded                = upsample (preamble, OSR);
preamble_filtered_by_gmsk           = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gmsk_twice     = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

%-------------------------------------------------------------------------
% Variables
%-------------------------------------------------------------------------
G_vRxRawDataBuff        = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData              = struct('nPntX', uint8(0), ...
                                'dSumX', uint32(0), ...
                                'dCntX', uint16(0), ...
                                'wAvrX', uint16(DC_MID_LEVEL), ...
                                'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData              = struct('wDotPattern', uint16(0), ...
                                'wDotChanged', uint8(0), ...
                                'wDotCountX', uint8(0));
G_wRxShiftReg           = 0;
G_dSwRxPllCntrX         = 0;
G_dSwRxPllSampP         = 0;
G_dSwRxPllSampC         = 0;
G_wRxCurrBitD           = 0;
G_wRxPrevBitD           = 0;
G_wCrcRegData           = 0;
G_wRxBitCount           = 0;

G_wRxAfAdcData          = 0;
G_wRxNrziCntr           = 0;
G_wRxNrziCurr           = 0;
G_wRxNrziPrev           = 0;
G_wRxNrziTemp           = 0;
G_wRxReferValue         = DC_MID_LEVEL;
G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue         = 0;
G_dRxAdcErrCnt          = 0;

G_wNewBitData           = 0;
G_bRxByteData           = 0;

G_PreStart              = 0;
G_PreOffset             = 150;
G_dSyncDetCnt           = 0;
G_dAdcErrCnt            = 0;
G_dStartErrCnt          = 0;
G_dPloadErrCnt          = 0;
G_dStuffErrCnt          = 0;
G_dCrcErrCnt            = 0;
G_dRcvPktCnt            = 0;

G_dRxAfAdcSumVal        = 0;
G_dRxAfAdcCntVal        = 0;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;
G_dSyncSymbolIndex      = 0;

if (DETECT_PREAMBLE == 0)
    G_dMaxSyncCorrVal   = 600;
else
    G_dMaxSyncCorrVal   = 630;
end
G_dMaxSyncCorrCnt       = length(impulse_response_of_gmsk)+7;
G_dDcOffset             = (G_dMaxSyncCorrCnt+15);
G_dDcOffsetAvgCnt       = 50;

if (DETECT_PREAMBLE == 0)
    G_SyncOffset        = 70;
else
    G_SyncOffset        = (OSR*6)+2;
end

G_dAdaptiveDcSymCnt     = (OSR*2);

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    G_hDumpFile = fopen('./DumpData/AisDumpData_ch1.bin');
else
    G_hDumpFile = fopen('./DumpData/AisDumpData_ch2.bin');
end
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

for nSmpCnt = 1:length(G_pSrcDataCh1)
    % Notch Filter
    if (ENABLE_NOTCH_FLT == 1)
        rX = G_pSrcDataCh1(nSmpCnt);
        rY = G_vNotchDataX(1) + NOTCH_FLT_B(1) * rX;
        G_vNotchDataX(1) = (NOTCH_FLT_B(2) * rX) + (NOTCH_FLT_A(1) * rY) + G_vNotchDataX(2);
        G_vNotchDataX(2) = (NOTCH_FLT_B(3) * rX) + (NOTCH_FLT_A(2) * rY) + G_vNotchDataX(3);
        G_wRxAfAdcData = floor(rY);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nSmpCnt);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = floor(G_wRxAfAdcData);

    % GMSK Filter
    if (ENABLE_GMSK_RX_FLT == 0)
        G_pFilteredData(nSmpCnt) = G_wRxAfAdcData;
    elseif (ENABLE_GMSK_RX_FLT == 1)
        nSum = 0;
        for idx = 1:RX_GMSK_BT_0_5_FIR_N
            nSum = nSum + (G_vRxRawDataBuff(idx) * G_vRxGmskCoefficient(idx));
        end
        G_wRxAfAdcData = floor(nSum / RX_GMSK_MAX_DATA_VALUE);
        %{
        conv_data = conv(G_vRxRawDataBuff, G_vRxGmskCoefficient);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N) / RX_GMSK_MAX_DATA_VALUE);
        %}
        G_pFilteredData(nSmpCnt) = G_wRxAfAdcData;
    elseif (ENABLE_GMSK_RX_FLT == 2)
        conv_data = conv(G_vRxRawDataBuff, impulse_response_of_gmsk);
        G_wRxAfAdcData = floor(conv_data(RX_GMSK_BT_0_5_FIR_N));
        G_pFilteredData(nSmpCnt) = G_wRxAfAdcData;
    end

    % Correlation Preamble
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if (nSmpCnt <= length(preamble_filtered_by_gmsk))
            CorrelPreamble(nSmpCnt) = 0;
        else
            if (ENABLE_GMSK_RX_FLT > 0)
                tmp_100 = G_pFilteredData(nSmpCnt - length(preamble_filtered_by_gmsk) + 1 : nSmpCnt);% - dc_level_100(nSmpCnt - LEN_PREAMBLE*OSR + 1 : nSmpCnt);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nSmpCnt) = (tmp_100) * preamble_filtered_by_gmsk / norm(preamble_filtered_by_gmsk);
                CorrelPreamble(nSmpCnt) = 1000*abs(CorrelPreamble(nSmpCnt));
            else
                tmp_100 = G_pSrcDataCh1(nSmpCnt - length(preamble_filtered_by_gmsk) + 1 : nSmpCnt);% - dc_level_100(nSmpCnt - LEN_PREAMBLE*OSR + 1 : nSmpCnt);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nSmpCnt) = (tmp_100)' * preamble_filtered_by_gmsk / norm(preamble_filtered_by_gmsk);
                CorrelPreamble(nSmpCnt) = 1000*abs(CorrelPreamble(nSmpCnt));
            end

            % Detected preamble
            if (CorrelPreamble(nSmpCnt) > G_dMaxSyncCorrVal && CorrelPreamble(nSmpCnt) > G_dMaxSyncCorrel)
                G_dMaxSyncCorrel = CorrelPreamble(nSmpCnt);
                G_dSyncSymbolIndex = nSmpCnt;
                G_dMaxSyncCnt = 0;
            elseif (G_dMaxSyncCorrel > G_dMaxSyncCorrVal)
                G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
            end
                
            if (G_dMaxSyncCorrel > G_dMaxSyncCorrVal && G_dMaxSyncCnt >= G_dMaxSyncCorrCnt)
                if (DETECT_PREAMBLE == 0)
                    etx = G_dSyncSymbolIndex - 45;
                    range = (etx-G_SyncOffset+1:etx);
                else
                    %range = (G_dSyncSymbolIndex-G_SyncOffset-1:G_dSyncSymbolIndex);
                    range = (nSmpCnt-G_dDcOffsetAvgCnt-G_dDcOffset+1:nSmpCnt-G_dDcOffset);
                end
                cnt = 0;
                for i = range
                    if (ENABLE_GMSK_RX_FLT > 0)
                        G_xPreData.dSumX = G_xPreData.dSumX + G_pFilteredData(i);
                    else
                        G_xPreData.dSumX = G_xPreData.dSumX + G_pSrcDataCh1(i);
                    end
                    cnt = cnt + 1;
                end
                G_xPreData.wAvrX = G_xPreData.dSumX / cnt;
                G_wRxReferValue = G_xPreData.wAvrX;

                if (nSmpCnt > G_PreOffset)
                    G_PreStart = nSmpCnt-G_PreOffset;
                else
                    G_PreStart = 1;
                end

                if (ENABLE_PLOT1 == 1)
                    h_fig1 = figure(1);
                    h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                    x1 = G_PreStart:nSmpCnt;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, CorrelPreamble(x1), '-+'); grid; 
                    title('Detected\_Preamble_Data'); yline(G_wRxReferValue,'-m',G_wRxReferValue,'LineWidth',2);
                    xline(G_dSyncSymbolIndex,'--', 'Picked sync correlation');
                    xline(nSmpCnt-G_dDcOffsetAvgCnt-G_SyncOffset+1,'--', 'DC Offset sum start');
                    xline(nSmpCnt-G_SyncOffset,'--', 'DC Offset sum end');
                end

                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;

                G_wRxRunStatus  = RX_MDM_STATUS_START;
                G_wRxShiftReg   = 0;
                G_wRxBitCount   = 0;
                G_wRxPrevBitD   = G_wRxNrziPrev;
                G_wBitSamplCntr = 1;
                G_dRxAdcErrCnt  = 0;

                G_dSwRxPllValue = RX_PLL_HALF;
                G_dSwRxPllCntrX = 1;
                G_dSwRxPllSampC = G_wRxNrziPrev;
                G_dSwRxPllSampP = G_wRxNrziPrev;

                G_xPreData.nPntX = 0;
                G_xPreData.dSumX = 0;
                G_xPreData.dCntX = 0;
                G_xPreData.wAvrX = DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;

                G_dRxAfAdcSumVal = 0;
                G_dRxAfAdcCntVal = 0;

                G_dSyncDetCnt = G_dSyncDetCnt + 1;
            end
        end
    end

    AdativeDcOffset(nSmpCnt) = G_wRxReferValue;

    % Check Max/Min Lever voltage range
    if (    (ENABLE_ADC_LIMIT == 1) && (G_pSrcDataCh1(nSmpCnt) < DC_MIN_LEVEL || G_pSrcDataCh1(nSmpCnt) > DC_MAX_LEVEL)) ...
        || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nSmpCnt) > DC_MAX_LEVEL))
    %if (    (ENABLE_ADC_LIMIT == 1) && (G_pFilteredData(nSmpCnt) < DC_MIN_LEVEL || G_pFilteredData(nSmpCnt) > DC_MAX_LEVEL)) ...
    %    || ((ENABLE_ADC_LIMIT == 2) && (G_pFilteredData(nSmpCnt) > DC_MAX_LEVEL))
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = 0;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        %if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if(G_wRxRunStatus <= RX_MDM_STATUS_START)
            G_dRxAdcErrCnt = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > 20)
                G_dRxAdcErrCnt = 0;

                if (ENABLE_PLOT96 == 1)
                    h_fig96 = figure(96);
                    h_fig96.Name = 'Adc Max/Min Error';
                    x1 = G_PreStart:nSmpCnt-G_SyncOffset+50;
                    x2 = G_PreStart:nSmpCnt-G_SyncOffset;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                    title('Error Max/Min Vol.');
                end

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    end

    if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
        if (ENABLE_GMSK_RX_FLT > 0)
            G_wRxAfAdcData = G_pFilteredData(nSmpCnt-G_SyncOffset);
        else
            G_wRxAfAdcData = G_pSrcDataCh1(nSmpCnt-G_SyncOffset);
        end

        if (G_wRxAfAdcData > G_wRxReferValue)
            G_wRxNrziCurr = 1;
        else
            G_wRxNrziCurr = 0;
        end

        G_dSwRxPllSampC = G_wRxNrziCurr;

        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
        % 아래 코드 사용시 수신율이 높아짐.
        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
            %if(G_wRxNrziCntr <= (OSR - 2))
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
                G_dSwRxPllSampC = G_dSwRxPllSampP;
            else
                G_wRxNrziCntr = 1;
            end
        else
            G_wRxNrziCntr = G_wRxNrziCntr + 1;
        end
        %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

        if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
            if (G_wRxRunStatus == RX_MDM_STATUS_START)
                if (G_dSwRxPllCntrX >= (OSR * 2 - 2) && G_dSwRxPllCntrX <= (OSR * 2 + 2))
                    G_dSwRxPllValue = RX_PLL_HALF + RX_PLL_STEP;
                end
            end
    
            if (G_dSwRxPllValue < RX_PLL_HALF)
                G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
            else
                G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
            end

            G_dSwRxPllCntrX = 1;
        else
            G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
        end

        G_dSwRxPllSampP = G_dSwRxPllSampC;

        G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
        if(G_dSwRxPllValue >= RX_PLL_FULL)
            G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
        else
            continue;
        end

        G_wRxCurrBitD = G_dSwRxPllSampC;

        %%%%% ProcessRxDataCommonRun()
        G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
        if (G_wRxCurrBitD == G_wRxPrevBitD)
            G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
            inversed = 0;
        else
            G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);

            if (ENABLE_ADAPT_DC_OFFSET == 1 || ENABLE_ADAPT_DC_OFFSET == 3)
                if (inversed == 2)
                    range2 = (nSmpCnt-G_SyncOffset-G_dAdaptiveDcSymCnt+1:nSmpCnt-G_SyncOffset);
                    sum2 = 0;
                    for i = range2
                        if (ENABLE_GMSK_RX_FLT > 0)
                            sum2 = sum2 + G_pFilteredData(i);
                        else
                            sum2 = sum2 + G_pSrcDataCh1(i);
                        end
                    end
                    if (ENABLE_ADAPT_DC_OFFSET == 1)
                        G_wRxReferValue = (G_wRxReferValue*0.5)  + ((sum2 / length(range2)) * 0.5);
                    else
                        G_wRxReferValue = (G_wRxReferValue*0.8)  + ((sum2 / length(range2)) * 0.2);
                    end
                end
                inversed = inversed + 1;
            elseif (ENABLE_ADAPT_DC_OFFSET == 2)
                if (inversed >= 2)
                    range2 = (nSmpCnt-G_SyncOffset-G_dAdaptiveDcSymCnt+1:nSmpCnt-G_SyncOffset);
                    sum2 = 0;
                    for i = range2
                        if (ENABLE_GMSK_RX_FLT > 0)
                            sum2 = sum2 + G_pFilteredData(i);
                        else
                            sum2 = sum2 + G_pSrcDataCh1(i);
                        end
                    end
                    G_wRxReferValue = (G_wRxReferValue*0.8)  + ((sum2 / length(range2)) * 0.2);
                end
                inversed = inversed + 1;
            end
        end

        G_wRxPrevBitD = G_wRxCurrBitD;

        switch (G_wRxRunStatus)
            case RX_MDM_STATUS_START
                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if (ENABLE_PLOT2 == 1)
                        h_fig2 = figure(2);
                        h_fig2.Name = 'Detected Start Data';
                        x1 = G_PreStart:nSmpCnt-G_SyncOffset;
                        x2 = G_PreStart:nSmpCnt-G_SyncOffset+800;
                        subplot(2,1,1); plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, AdativeDcOffset(x1), '-m'); grid; 
                                        title('detected\_start\_data');
                        subplot(2,1,2); plot(x2, G_pSrcDataCh1(x2), '-x', x1, G_pFilteredData(x1), '-o'); grid; 
                                        title('filtered\_start\_data');
                    end

                    %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                    %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();
                    G_wRxBitCount    = 0;
                    G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                else
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= 32)
                        if (ENABLE_PLOT97 == 1)
                            h_fig97 = figure(97);
                            h_fig97.Name = 'Start Bit Error';
                            x1 = G_PreStart:nSmpCnt-G_SyncOffset+50;
                            x2 = G_PreStart:nSmpCnt-G_SyncOffset;
                            plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                            title('Error Start Bit');
                        end

                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStartErrCnt = G_dStartErrCnt + 1;
                    end
                end

            case RX_MDM_STATUS_PRELOAD
                G_wRxBitCount = G_wRxBitCount + 1;
                if (G_wRxBitCount == 8)
                    G_wRxBitCount = 0;
                    G_wCrcRegData = 0xffff;
                    G_wRxRunStatus= RX_MDM_STATUS_DATA;
                    %ClrRxRawFormTemp();

                    nTemp = bitshift(G_wRxShiftReg, 2);
                    nTemp = bitand(nTemp, 0x00ff);
                    nMsgID = G_vReverDataTableX(nTemp + 1);
                    if (nMsgID < 0)
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dPloadErrCnt = G_dPloadErrCnt + 1;
                    else
                        m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                    end
                end

            case RX_MDM_STATUS_DATA
                if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if(G_wRxBitCount >= (m_wRxMaxBitSize))
                        if (ENABLE_PLOT98 == 1)
                            h_fig98 = figure(98);
                            h_fig98.Name = 'Stuffing Bit Error';
                            x1 = G_PreStart:nSmpCnt-G_SyncOffset+50;
                            x2 = G_PreStart:nSmpCnt-G_SyncOffset;
                            plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                            title('Error Stuffing Bit');
                        end

                        %%%%%% ResetToRxStatusPreamble()
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_bRxByteData = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_dStuffErrCnt = G_dStuffErrCnt + 1;
                        continue;
                    end

                    G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                    G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));

                    %if(bitand(G_wRxBitCount, 0x07) == 0)
                    %    PutDataIntoRxRawBuff(G_bRxByteData);
                    %end

                    if (bitand(bitxor(G_wCrcRegData, G_wNewBitData), 0x0001))           % Pass new bit through CRC calculation
                        G_wCrcRegData = bitxor(bitshift(G_wCrcRegData, -1), 0x8408);   % Xor with the CRC polynomial (X^16 + X^12 + X^5 + 1)
                    else
                        G_wCrcRegData = bitshift(G_wCrcRegData, -1);
                    end
                end

                if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                        if (ENABLE_PLOT3 == 1)
                            h_fig3 = figure(3);
                            h_fig3.Name = 'Received Data(CRC OK)';
                            x1 = G_PreStart:nSmpCnt-G_SyncOffset+50;
                            if (G_PreStart <= G_SyncOffset)
                                x2 = G_PreStart:nSmpCnt;
                                x3 = G_PreStart:nSmpCnt;
                            else
                                x2 = G_PreStart:nSmpCnt-G_SyncOffset;
                                x3 = G_PreStart+G_SyncOffset:nSmpCnt;
                            end
                            plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x3), '-m'); grid; 
                            title('Received AIS Packet');
                        end

                        %WritePacketIntoRxRawBuff();
                        G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                        G_wRxShiftReg  = 0;
                        G_wRxReferValue= DC_MID_LEVEL;
                        G_bRxByteData = 0;
                        G_dRcvPktCnt = G_dRcvPktCnt + 1;
                    else
                        if (ENABLE_PLOT99 == 1)
                            h_fig99 = figure(99);
                            h_fig99.Name = 'Received Data(CRC ERROR)';
                            x1 = G_PreStart:nSmpCnt-G_SyncOffset+50;
                            if (G_PreStart <= G_SyncOffset)
                                x2 = G_PreStart:nSmpCnt;
                                x3 = G_PreStart:nSmpCnt;
                            else
                                x2 = G_PreStart:nSmpCnt-G_SyncOffset;
                                x3 = G_PreStart+G_SyncOffset:nSmpCnt;
                            end
                            plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x3), '-m'); grid; 
                            title('Received Error AIS Packet');
                        end

                        %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                        %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                        G_wRxBitCount    = 0;
                        %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                        G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                        G_bRxByteData    = 0;
                        G_dCrcErrCnt     = G_dCrcErrCnt + 1;
                    end
                end

            otherwise
                warning('Unexpected run status.');
        end
    end
end

figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "PloadErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dPloadErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y);
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')
