clear;

%------------------------------------------------------------------------
% AIS 수신기 with Viterbi MLSD Algorithm (v3 적용)
% 기존 코드에 진정한 Viterbi MLSD 알고리즘 통합
%------------------------------------------------------------------------

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
USE_CHx_RAW_DATA        = 1;                % 0: Ch1, 1: Ch2 Raw data
ENABLE_NOTCH_FLT        = 1;                % 0: Disable, 1: Enable Notch Filter
ENABLE_GMSK_RX_FLT      = 1;                % 0: Disable, 1: New GMSK Filter
ENABLE_ADAPT_DC_OFFSET  = 1;                % 0: Disable, 1: Adaptive DC Offset
ENABLE_ADC_LIMIT        = 2;                % 0: Disable, 1: Min/Max Enable, 2: Max only Enable
ENABLE_VITERBI_MLSD     = 1;                % 0: Disable, 1: Enable Viterbi MLSD
%-------------------------------------------------------------------------
ENABLE_DEBUG            = 0;
if (ENABLE_DEBUG == 1)
    ENABLE_PLOT1        = 1;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 1;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 1;                % 0: Disable, 1: Received Data packet with CRC
else
    ENABLE_PLOT1        = 0;                % 0: Disable, 1: Sync detection (Matched filter)
    ENABLE_PLOT2        = 0;                % 0: Disable, 1: Start detection
    ENABLE_PLOT3        = 0;                % 0: Disable, 1: Received Data packet with CRC
end

ENABLE_DEBUG_ERROR      = 0;
if (ENABLE_DEBUG_ERROR == 1)
    ENABLE_PLOT96       = 1;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 1;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 1;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 1;                % 0: Disable, 1: CRC Error
else
    ENABLE_PLOT96       = 0;                % 0: Disable, 1: ADC Max/Min Error
    ENABLE_PLOT97       = 0;                % 0: Disable, 1: Start Bit Error
    ENABLE_PLOT98       = 0;                % 0: Disable, 1: Stuffing Bit Error
    ENABLE_PLOT99       = 0;                % 0: Disable, 1: CRC Error
end

%-------------------------------------------------------------------------
BIT_RATE                = 9600;             % Bit rate
OSR                     = 5;                % Over sampling rate
BT                      = 0.4;              % Transmit BT product
RX_BT                   = 0.5;              % Receive BT product
LEN_PSF                 = 8 * OSR;          % Pulse shaping filter length
H_NORM                  = 3;                % 1: normalized by h_max, 2: normalized by norm(h), 3: no normalized

ADC_RES                 = 12;               % 12bit resolution
ADC_MAX_VALUE           = 4095;
ADC_MAX_ERROR_CNT       = 30;

%-------------------------------------------------------------------------
MAX_SYNC_CORRVAL        = .750;
MAX_SYNC_COUNT          = 25;
DC_AVG_OFFSET           = (OSR*5);
DC_AVG_COUNT            = 55;
DC_GAP                  = 0.000;
START_DETECT_OFFSET     = (OSR*8);
SYNC_DETECT_OFFSET      = (OSR*6)+2;
ADC_SUB_DC_OFFSET       = 1450;

%-------------------------------------------------------------------------
 % Legacy modem defines
 NOTCH_FLT_A            = [+1.999986841577810, -0.999986910116283];
 NOTCH_FLT_B            = [+0.999993455058141, -1.999986841577810, +0.999993455058141];
 
 DC_MIN_LEVEL           = (   0 / 3300);    % 0.05V
 DC_MID_LEVEL           = (1000 / 3300);    % 1.00V
 DC_MAX_LEVEL           = (1850 / 3300);    % 1.85V

 RX_PLL_FULL            = 2400;
 RX_PLL_HALF            = (RX_PLL_FULL / 2);
 RX_PLL_INCR            = (RX_PLL_FULL / OSR);
 RX_PLL_STEP            = (RX_PLL_INCR / 3);

 RX_GMSK_BT_0_4_FIR_N   = 17;
 RX_GMSK_BT_0_5_FIR_N   = 13;
 RX_GMSK_TO_INT_FACTOR  = 16;

 RX_GMSK_MAX_DATA_VALUE = (BIT_RATE*OSR*RX_GMSK_TO_INT_FACTOR);

 RX_MDM_STATUS_PREAMBLE = 0;
 RX_MDM_STATUS_START    = 1;
 RX_MDM_STATUS_PRELOAD  = 2;
 RX_MDM_STATUS_DATA     = 3;

 RX_DOT_MAX_CNT_SIZE    = 7;
 RX_DOT_MAX_CNT_MASK    = 0x7f;
 RX_DOT_START_P_MASK    = 0x05;
 RX_DOT_DETCT_P_MASK    = 0x55;
 RX_DOT_MAX_CNT_LAST    = RX_DOT_MAX_CNT_SIZE;

 RX_PRE_MAX_CNT_SIZE    = 12;
 RX_PRE_MAX_BUF_SIZE    = (RX_PRE_MAX_CNT_SIZE * OSR);

 G_vNotchDataX = zeros(1, 3);

 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];

%-------------------------------------------------------------------------
dot_pattern             = repmat([1, 1, 0, 0], 1, 6);       % Dot pattern
preamble                = dot_pattern';                     % Preamble (dot pattern only)
preamble_os             = repelem(preamble, OSR);           % Over sampled preamble
LEN_DOT_PATTERN         = length(dot_pattern);              % Length of dot pattern
LEN_PREAMBLE            = length(preamble);                 % Length of preamble
LEN_PREAMBLE_OS         = LEN_PREAMBLE*OSR;                 % Length of over sampled preamble
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% Parameters:
% BT: 대역폭-시간 곱
% OSR: 오버샘플링 비율
% LENGTH: 임펄스 응답의 길이
% NORM: Normalize method
function [h, t] = gmsk_impulse_response(BT, OSR, LENGTH, NORM)
% h: impulse response
% t: time index
    t = ((-LENGTH / 2):(LENGTH / 2)) / OSR;
    h = 0.5 * (erf(pi * BT * sqrt(2 / log(2)) * (t + 0.5)) ...
             - erf(pi * BT * sqrt(2 / log(2)) * (t - 0.5)));

    if (NORM == 1)
        h = h / max(h);
    elseif (NORM == 2)
        h = h / norm(h);
    end
end

% Notch Filter 적용
function y = apply_notch_filter(x, notch_state, NOTCH_FLT_A, NOTCH_FLT_B)
    rY = notch_state(1) + NOTCH_FLT_B(1) * x;
    notch_state(1) = (NOTCH_FLT_B(2) * x) + (NOTCH_FLT_A(1) * rY) + notch_state(2);
    notch_state(2) = (NOTCH_FLT_B(3) * x) + (NOTCH_FLT_A(2) * rY) + notch_state(3);
    y = rY;
end

%------------------------------------------------------------------------
% GMSK Filter 적용
function y = apply_gmsk_filter(x_buff, impulse_response)
    conv_data = conv(x_buff, impulse_response);
    y = conv_data(length(impulse_response));
end

%------------------------------------------------------------------------
% Adaptive DC Offset 보정 (반전 구간에서만)
function G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc)
    G_wRxReferValue = alpha_dc * G_wRxReferValue + (1-alpha_dc) * sample;
end

%------------------------------------------------------------------------
% NRZI 해석
function bit = nrzi_decode(sample, refer)
    if sample > refer
        bit = 1;
    else
        bit = 0;
    end
end

%------------------------------------------------------------------------
% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end

%------------------------------------------------------------------------
% Viterbi MLSD 디코딩 (v3에서 성공한 알고리즘)
function [detected_bits, confidence, viterbi_state] = viterbi_mlsd_decode(signal, viterbi_state)
    VITERBI_TRACEBACK_DEPTH = 256;  % 로컬 상수로 정의

    N = length(signal);
    if N < 16
        detected_bits = [];
        confidence = 0;
        return;
    end

    % 4-상태 Viterbi MLSD (GMSK)
    num_states = 4;
    INF = 1e10;

    % 경로 메트릭 초기화
    path_metrics = INF * ones(num_states, N+1);
    path_metrics(1, 1) = 0;  % 초기 상태

    % 생존 경로 저장
    survivor_paths = zeros(num_states, N);
    survivor_bits = zeros(num_states, N);

    % Viterbi 알고리즘 메인 루프
    for t = 1:N
        new_path_metrics = INF * ones(num_states, 1);
        new_survivor_paths = zeros(num_states, 1);
        new_survivor_bits = zeros(num_states, 1);

        for curr_state = 1:num_states
            best_metric = INF;
            best_prev_state = 1;
            best_bit = 0;

            for prev_state = 1:num_states
                for input_bit = 0:1
                    % 상태 전이 계산
                    [next_state, is_valid] = compute_gmsk_state_transition(prev_state, input_bit);

                    if is_valid && next_state == curr_state
                        % 예상 신호 계산
                        expected_signal = compute_gmsk_expected_signal(prev_state, curr_state, ...
                            viterbi_state.h0, viterbi_state.h1, viterbi_state.bias);

                        % 브랜치 메트릭
                        error = signal(t) - expected_signal;
                        branch_metric = (error^2) / (2 * viterbi_state.noise_var);

                        % 총 경로 메트릭
                        total_metric = path_metrics(prev_state, t) + branch_metric;

                        if total_metric < best_metric
                            best_metric = total_metric;
                            best_prev_state = prev_state;
                            best_bit = input_bit;
                        end
                    end
                end
            end

            new_path_metrics(curr_state) = best_metric;
            new_survivor_paths(curr_state) = best_prev_state;
            new_survivor_bits(curr_state) = best_bit;
        end

        path_metrics(:, t+1) = new_path_metrics;
        survivor_paths(:, t) = new_survivor_paths;
        survivor_bits(:, t) = new_survivor_bits;
    end

    % 역추적
    [min_metric, best_final_state] = min(path_metrics(:, end));
    traceback_length = min(N, VITERBI_TRACEBACK_DEPTH);
    detected_bits = zeros(1, traceback_length);
    current_state = best_final_state;

    for t = traceback_length:-1:1
        detected_bits(t) = survivor_bits(current_state, t);
        current_state = survivor_paths(current_state, t);
    end

    % 신뢰도 계산
    if min_metric < INF && traceback_length > 0
        normalized_metric = min_metric / traceback_length;
        confidence = exp(-normalized_metric / max(viterbi_state.noise_var, 0.1));
        confidence = max(0.01, min(1.0, confidence));
    else
        confidence = 0.01;
    end

    % 채널 계수 적응형 업데이트
    if confidence > 0.3
        update_rate = 0.01;
        viterbi_state.noise_var = (1-update_rate) * viterbi_state.noise_var + update_rate * var(signal);
    end
end

%------------------------------------------------------------------------
% GMSK 상태 전이 계산
function [next_state, is_valid] = compute_gmsk_state_transition(prev_state, input_bit)
    is_valid = true;

    % 이전 심벌 추출
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌 계산 (NRZI)
    if input_bit == 0
        curr_symbol = prev_symbol;
    else
        curr_symbol = -prev_symbol;
    end

    % 위상 변화 계산
    if input_bit == 0
        if prev_state == 1 || prev_state == 3
            phase_change = 0;
        else
            phase_change = 1;
        end
    else
        if prev_state == 1 || prev_state == 3
            phase_change = 1;
        else
            phase_change = 0;
        end
    end

    % 다음 상태 결정
    if curr_symbol == -1
        if phase_change == 0
            next_state = 1;
        else
            next_state = 2;
        end
    else
        if phase_change == 0
            next_state = 3;
        else
            next_state = 4;
        end
    end
end

%------------------------------------------------------------------------
% GMSK 예상 신호 계산
function expected_signal = compute_gmsk_expected_signal(prev_state, curr_state, h0, h1, bias)
    % 이전 심벌
    if prev_state <= 2
        prev_symbol = -1;
    else
        prev_symbol = +1;
    end

    % 현재 심벌
    if curr_state <= 2
        curr_symbol = -1;
    else
        curr_symbol = +1;
    end

    % 채널 모델
    expected_signal = h0 * curr_symbol + h1 * prev_symbol + bias;
end


%-------------------------------------------------------------------------
% impulse response of gmsk filter
%-------------------------------------------------------------------------
%SPAN = 3; SPS = 4;
%impulse_response_of_gmsk            = gmsk_impulse_response(BT, OSR, SPAN*SPS, 1);
SPAN = 3; SPS = 4;
impulse_response_of_gmsk            = gaussdesign(BT, SPAN, SPS);
impulse_response_of_gmsk_twice      = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);
RX_GMSK_BT_0_5_FIR_N                = length(impulse_response_of_gmsk);

preamble_zero_padded                = upsample (preamble, OSR);
preamble_filtered_by_gmsk           = conv (preamble_zero_padded, impulse_response_of_gmsk);
preamble_filtered_by_gmsk_twice     = conv (preamble_zero_padded, impulse_response_of_gmsk_twice);

if (ENABLE_PLOT1 == 1)
    h_fig10 = figure(10);
    h_fig10.Name = 'gmsk and preamble';
    subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk) (o)');
    subplot(3,1,2); plot(preamble, '-o'); grid; title('preamble');
    subplot(3,1,3); plot(preamble_filtered_by_gmsk, '-o');
end

%-------------------------------------------------------------------------
% Variables
%-------------------------------------------------------------------------
G_vRxRawDataBuff        = zeros(1, RX_GMSK_BT_0_5_FIR_N);
G_xPreData              = struct('nPntX', uint8(0), ...
                                'dSumX', double(0), ...
                                'dCntX', uint16(0), ...
                                'wAvrX', double(DC_MID_LEVEL), ...
                                'vData', zeros(1,RX_PRE_MAX_BUF_SIZE));
G_xDotData              = struct('wDotPattern', uint16(0), ...
                                'wDotChanged', uint8(0), ...
                                'wDotCountX', uint8(0));
G_wRxShiftReg           = 0;
G_dSwRxPllCntrX         = 0;
G_dSwRxPllSampP         = 0;
G_dSwRxPllSampC         = 0;
G_wRxCurrBitD           = 0;
G_wRxPrevBitD           = 0;
G_wCrcRegData           = 0;
G_wRxBitCount           = 0;

G_wRxAfAdcData          = 0;
G_wRxNrziCntr           = 0;
G_wRxNrziCurr           = 0;
G_wRxNrziPrev           = 0;
G_wRxNrziTemp           = 0;
G_wRxReferValue         = DC_MID_LEVEL;
G_wRxRunStatus          = RX_MDM_STATUS_PREAMBLE;
G_dSwRxPllValue         = 0;
G_dRxAdcErrCnt          = 0;

G_wNewBitData           = 0;
G_bRxByteData           = 0;

G_PreStart              = 0;
G_PreOffset             = 300;
G_dSyncDetCnt           = 0;
G_dAdcErrCnt            = 0;
G_dStartErrCnt          = 0;
G_dPloadErrCnt          = 0;
G_dStuffErrCnt          = 0;
G_dCrcErrCnt            = 0;
G_dRcvPktCnt            = 0;

G_dRxAfAdcSumVal        = 0;
G_dRxAfAdcCntVal        = 0;
G_dMaxSyncCorrel        = 0;
G_dMaxSyncCnt           = 0;
G_dSyncSymbolIndex      = 0;
G_dStartSymbolIndex     = 0;

G_BitDataArray = zeros(1, 500);

% 개선된 성능을 위한 추가 변수들
G_CorrelHistory         = zeros(1, 100);    % 상관관계 히스토리
G_AdaptiveThreshold     = MAX_SYNC_CORRVAL; % 적응형 임계값
G_NoiseFloor            = 0.1;              % 노이즈 플로어
G_SignalQuality         = 0;                % 신호 품질 지표
G_HysteresisValue       = 0.02;             % NRZI 히스테리시스 값

% Viterbi MLSD 파라미터 (v3에서 성공한 설정)
VITERBI_WINDOW_SIZE     = 256;              % AIS 최대 패킷 크기
VITERBI_TRACEBACK_DEPTH = 256;              % 전체 패킷 역추적
VITERBI_CONFIDENCE_THRESHOLD = 0.05;        % 신뢰도 임계값 (완화)
CHANNEL_UPDATE_RATE     = 0.15;             % 채널 업데이트 비율
VITERBI_INITIAL_H0      = 0.8;              % 초기 h0 값
VITERBI_INITIAL_H1      = -0.4;             % 초기 h1 값
SYNC_RATIO_THRESHOLD    = 0.25;             % 동기 일치율 임계값 (완화)

% Viterbi MLSD 전역 변수
global G_ViterbiStats G_vAisSyncPattern;
G_ViterbiStats = struct();
G_ViterbiStats.corrected_packets = 0;       % 수정된 패킷 수
G_ViterbiStats.confidence_history = [];     % 신뢰도 히스토리
G_ViterbiStats.channel_estimates = [];
G_ViterbiStats.packet_positions = [];

% 패킷 신호 수집용 전역 변수
G_PacketSignal = [];                         % 현재 패킷의 신호



% AIS 동기 패턴 (8비트): 01111110
G_vAisSyncPattern = [0 1 1 1 1 1 1 0];

% Viterbi 상태 초기화
G_ViterbiState = struct();
G_ViterbiState.h0 = VITERBI_INITIAL_H0;
G_ViterbiState.h1 = VITERBI_INITIAL_H1;
G_ViterbiState.bias = 0.0;
G_ViterbiState.noise_var = 1.0;
G_ViterbiState.path_metrics = zeros(4, 1);
G_ViterbiState.survivor_paths = zeros(4, 256);



%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
if (USE_CHx_RAW_DATA == 0)
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch1.bin');
else
    G_hDumpFile = fopen('./DumpData/DUMPDATA_250525_ch2.bin');
end
G_pSrcDataCh1 = fread(G_hDumpFile, 'uint16');

G_pSrcDataCh1 = (G_pSrcDataCh1-ADC_SUB_DC_OFFSET) / ADC_MAX_VALUE;

% 성능 향상을 위한 배열 사전 할당
data_length = length(G_pSrcDataCh1);
G_pFilteredData = zeros(1, data_length);
CorrelPreamble = zeros(1, data_length);
AdativeDcOffset = zeros(1, data_length);
BitArray = zeros(1, data_length);
G_dCRCErrSymIdx = zeros(1, 1000);  % CRC 오류 인덱스 배열 사전 할당

for nCurSymbolIdx = 1:length(G_pSrcDataCh1)
    % Notch Filter
    if (ENABLE_NOTCH_FLT == 1)
        G_wRxAfAdcData = apply_notch_filter(G_pSrcDataCh1(nCurSymbolIdx), G_vNotchDataX, NOTCH_FLT_A, NOTCH_FLT_B);
    else
        G_wRxAfAdcData = G_pSrcDataCh1(nCurSymbolIdx);
    end

    G_vRxRawDataBuff(1:1) = [];
    G_vRxRawDataBuff(RX_GMSK_BT_0_5_FIR_N) = G_wRxAfAdcData;

    % GMSK Filter
    G_pFilteredData(nCurSymbolIdx) = apply_gmsk_filter(G_vRxRawDataBuff, impulse_response_of_gmsk);

    G_vGmskPreamble = preamble_filtered_by_gmsk;
    % Correlation Preamble
    if (G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        if (nCurSymbolIdx <= length(G_vGmskPreamble))
            CorrelPreamble(nCurSymbolIdx) = 0;
        else
            % Preamble correlation detection은 GMSK Filter를 통과한 데이터를 사용하지 않는다.
            %if (ENABLE_GMSK_RX_FLT > 0)
            %    tmp_100 = G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
            %    tmp_100 = tmp_100/norm(tmp_100);
            %    CorrelPreamble(nCurSymbolIdx) = (tmp_100) * G_vGmskPreamble / norm(G_vGmskPreamble);
            %else
                tmp_100 = G_pSrcDataCh1(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx);
                tmp_100 = tmp_100/norm(tmp_100);
                CorrelPreamble(nCurSymbolIdx) = (tmp_100)' * G_vGmskPreamble / norm(G_vGmskPreamble);
            %end

            % 개선된 프리앰블 검출 (적응형 임계값 사용)
            % 상관관계 히스토리 업데이트
            G_CorrelHistory = [G_CorrelHistory(2:end), CorrelPreamble(nCurSymbolIdx)];

            % 신호 품질 평가
            recent_correl = G_CorrelHistory(max(1, end-10):end);
            G_SignalQuality = mean(recent_correl) / (std(recent_correl) + 0.01);

            if (CorrelPreamble(nCurSymbolIdx) > MAX_SYNC_CORRVAL && CorrelPreamble(nCurSymbolIdx) > G_dMaxSyncCorrel)
                G_dMaxSyncCorrel = CorrelPreamble(nCurSymbolIdx);
                G_dSyncSymbolIndex = nCurSymbolIdx;
                G_dMaxSyncCnt = 0;
            elseif (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL)
                G_dMaxSyncCnt = G_dMaxSyncCnt + 1;
            end
                
            if (G_dMaxSyncCorrel > MAX_SYNC_CORRVAL && G_dMaxSyncCnt >= MAX_SYNC_COUNT)
                range = (G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1:G_dSyncSymbolIndex-DC_AVG_OFFSET);
                if (ENABLE_GMSK_RX_FLT > 0)
                    dc_sum = sum(G_pFilteredData(range));
                else
                    dc_sum = sum(G_pSrcDataCh1(range));
                end
                G_wRxReferValue = dc_sum / length(range) + DC_GAP;

                if (nCurSymbolIdx > G_PreOffset)
                    G_PreStart = nCurSymbolIdx-G_PreOffset;
                else
                    G_PreStart = 1;
                end

                % 패킷 시작 시 신호 수집 초기화
                if ENABLE_VITERBI_MLSD == 1
                    G_PacketSignal = [];
                end



                %--------------------------------------------
                if (ENABLE_PLOT1 == 1)
                    h_fig1 = figure(1);
                    h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                    x1 = G_PreStart:nCurSymbolIdx;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, CorrelPreamble(x1), '-+'); grid; 
                    title('Detected\_Preamble_Data'); yline(G_wRxReferValue,'-m',G_wRxReferValue,'LineWidth',2);
                    xline(G_dSyncSymbolIndex,'--', 'Max picked correlation');
                    xline(G_dSyncSymbolIndex-DC_AVG_COUNT-DC_AVG_OFFSET+1,'--', 'DC Offset sum start');
                    xline(G_dSyncSymbolIndex-DC_AVG_OFFSET,'--', 'DC Offset sum end');
                end
                %--------------------------------------------

                %index_max = find(impulse_response_of_gmsk == max(impulse_response_of_gmsk));
                %half_impulse_response_os = impulse_response_of_gmsk(index_max:(index_max+2*OSR));
                %calc_coefficient(G_pFilteredData(nCurSymbolIdx - length(G_vGmskPreamble) + 1 : nCurSymbolIdx), G_vGmskPreamble', half_impulse_response_os);

                %% 
                G_dStartSymbolIndex = nCurSymbolIdx - START_DETECT_OFFSET;
                G_dMaxSyncCorrel= 0;
                G_dMaxSyncCnt   = 0;

                G_wRxRunStatus  = RX_MDM_STATUS_START;
                G_wRxShiftReg   = 0;
                G_wRxBitCount   = 0;
                G_wRxPrevBitD   = G_wRxNrziPrev;
                G_wBitSamplCntr = 1;
                G_dRxAdcErrCnt  = 0;

                G_dSwRxPllValue = RX_PLL_HALF;
                G_dSwRxPllCntrX = 1;
                G_dSwRxPllSampC = G_wRxNrziPrev;
                G_dSwRxPllSampP = G_wRxNrziPrev;

                G_xPreData.nPntX = 0;
                G_xPreData.dSumX = 0;
                G_xPreData.dCntX = 0;
                G_xPreData.wAvrX = DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;

                G_dRxAfAdcSumVal = 0;
                G_dRxAfAdcCntVal = 0;

                G_dSyncDetCnt = G_dSyncDetCnt + 1;
            end
        end
    end

    AdativeDcOffset(nCurSymbolIdx) = G_wRxReferValue;

    % Check Max/Min Lever voltage range
    if (    (ENABLE_ADC_LIMIT == 1) && (G_pSrcDataCh1(nCurSymbolIdx) < DC_MIN_LEVEL || G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
        || ((ENABLE_ADC_LIMIT == 2) && (G_pSrcDataCh1(nCurSymbolIdx) > DC_MAX_LEVEL))
    %if (    (ENABLE_ADC_LIMIT == 1) && (G_pFilteredData(nCurSymbolIdx) < DC_MIN_LEVEL || G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL)) ...
    %    || ((ENABLE_ADC_LIMIT == 2) && (G_pFilteredData(nCurSymbolIdx) > DC_MAX_LEVEL))
        G_xPreData.nPntX = 0;
        G_xPreData.dSumX = 0;
        G_xPreData.dCntX = 0;
        G_xPreData.wAvrX = DC_MID_LEVEL;

        if(G_wRxRunStatus == RX_MDM_STATUS_PREAMBLE)
        %if(G_wRxRunStatus <= RX_MDM_STATUS_START)
            G_dRxAdcErrCnt = 0;
            % Init preamble correlation value and count
            % AD Error의 경우 correlation max value and count를 초기화 해주면 수신 성능이
            % 좋아짐.
            G_dMaxSyncCorrel= 0;
            G_dMaxSyncCnt   = 0;
        else
            G_dRxAdcErrCnt = G_dRxAdcErrCnt + 1;
            if(G_dRxAdcErrCnt > ADC_MAX_ERROR_CNT)
                G_dRxAdcErrCnt = 0;

                %--------------------------------------------
                if (ENABLE_PLOT96 == 1)
                    h_fig96 = figure(96);
                    h_fig96.Name = 'Adc Max/Min Error';
                    x1 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET+50;
                    x2 = G_PreStart:nCurSymbolIdx-SYNC_DETECT_OFFSET;
                    plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m'); grid; 
                    title('Error Max/Min Vol.');
                end
                %--------------------------------------------

                G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                G_wRxShiftReg  = 0;
                G_wRxReferValue= DC_MID_LEVEL;

                G_xDotData.wDotPattern = 0;
                G_xDotData.wDotChanged = 0;
                G_xDotData.wDotCountX = 0;
                G_dAdcErrCnt = G_dAdcErrCnt+1;
            end
        end
    end

    if (G_wRxRunStatus ~= RX_MDM_STATUS_PREAMBLE)
        for idx = (G_dStartSymbolIndex : nCurSymbolIdx)
            if (ENABLE_GMSK_RX_FLT > 0)
                G_wRxAfAdcData = G_pFilteredData(idx);
            else
                G_wRxAfAdcData = G_pSrcDataCh1(idx);
            end

            % Viterbi MLSD 또는 기존 NRZI 디코딩 선택
            if ENABLE_VITERBI_MLSD == 1
                % Viterbi MLSD 사용 (패킷 단위로 처리)
                G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
            else
                % 기존 NRZI 디코딩
                G_wRxNrziCurr = nrzi_decode(G_wRxAfAdcData, G_wRxReferValue);
            end
            G_dSwRxPllSampC = G_wRxNrziCurr;

            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
            % 아래 코드 사용시 수신율이 높아짐.
            %
            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if((G_wRxNrziCntr <= (OSR - 2) || (G_wRxNrziCntr == (OSR - 1) && G_dSwRxPllValue >= (RX_PLL_FULL - RX_PLL_INCR + RX_PLL_STEP))))
                %if(G_wRxNrziCntr <= (OSR - 2))
                    G_wRxNrziCntr = G_wRxNrziCntr + 1;
                    G_dSwRxPllSampC = G_dSwRxPllSampP;
                else
                    G_wRxNrziCntr = 1;
                end
            else
                G_wRxNrziCntr = G_wRxNrziCntr + 1;
            end
            %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

            if (G_dSwRxPllSampC ~= G_dSwRxPllSampP)
                if (G_wRxRunStatus == RX_MDM_STATUS_START)
                    if (G_dSwRxPllCntrX >= (OSR * 2 - 3) && G_dSwRxPllCntrX <= (OSR * 2 + 3))
                        G_dSwRxPllValue = RX_PLL_HALF;
                    end
                end
        
                if (G_dSwRxPllValue < RX_PLL_HALF)
                    G_dSwRxPllValue = (G_dSwRxPllValue + RX_PLL_STEP);
                else
                    G_dSwRxPllValue = (G_dSwRxPllValue - RX_PLL_STEP);
                end

                G_dSwRxPllCntrX = 1;
            else
                G_dSwRxPllCntrX = G_dSwRxPllCntrX + 1;
            end

            G_dSwRxPllSampP = G_dSwRxPllSampC;

            G_dSwRxPllValue = G_dSwRxPllValue + RX_PLL_INCR;
            if(G_dSwRxPllValue >= RX_PLL_FULL)
                G_dSwRxPllValue = G_dSwRxPllValue - RX_PLL_FULL;
            else
                continue;
            end

            G_wRxCurrBitD = G_dSwRxPllSampC;

            %%%%% ProcessRxDataCommonRun()
            G_wRxShiftReg = bitshift(G_wRxShiftReg, 1);
            if (G_wRxCurrBitD == G_wRxPrevBitD)
                G_wRxShiftReg = bitor(G_wRxShiftReg, 0x0001);
                BitArray(idx) = 0.05;
            else
                G_wRxShiftReg = bitand(G_wRxShiftReg, 0xfffe);
                BitArray(idx) = 0.015;

                if (ENABLE_ADAPT_DC_OFFSET == 1)
                    % 개선된 적응형 DC Offset 추적
                    % 신호 품질에 따른 적응형 알파 값
                    if G_SignalQuality > 2.5
                        alpha_dc = 0.900;  % 신호가 좋으면 더 빠른 적응
                    elseif G_SignalQuality > 1.5
                        alpha_dc = 0.850;  % 기본값
                    else
                        alpha_dc = 0.800;  % 신호가 나쁘면 더 느린 적응
                    end

                    % 반전 구간에서만 DC Offset 보정
                    if (ENABLE_GMSK_RX_FLT > 0)
                        sample = G_pFilteredData(idx);
                    else
                        sample = G_pSrcDataCh1(idx);
                    end
                    G_wRxReferValue = update_dc_offset_on_invert(G_wRxReferValue, sample, alpha_dc);
                end
            end

            G_wRxPrevBitD = G_wRxCurrBitD;

            switch (G_wRxRunStatus)
                case RX_MDM_STATUS_START
                    % 개선된 시작 비트 검출 (더 유연한 패턴 매칭)
                    start_pattern_detected = false;

                    % 다양한 시작 패턴 검사 (노이즈에 더 강건)
                    if (bitand(G_wRxShiftReg, 0x003f) == 0x003e || ...
                        bitand(G_wRxShiftReg, 0x001f) == 0x001e || ...
                        bitand(G_wRxShiftReg, 0x007f) == 0x007e)
                        start_pattern_detected = true;
                    end

                    if start_pattern_detected
                        %%----------------------------------------------------------
                        if (ENABLE_PLOT2 == 1)
                            h_fig2 = figure(2);
                            h_fig2.Name = 'Detected Start Data';
                            x1 = G_PreStart:idx;
                            x2 = G_PreStart:idx;
                            subplot(2,1,1); plot(x1, G_pSrcDataCh1(x1), '-x', x1, G_pFilteredData(x1), '-o', x1, AdativeDcOffset(x1), '-m'); grid; 
                                            title('detected\_start\_data');
                            subplot(2,1,2); plot(x2, G_pSrcDataCh1(x2), '-x', x1, G_pFilteredData(x1), '-o'); grid; 
                                            title('filtered\_start\_data');
                        end
                        %----------------------------------------------------------

                        G_wRxBitCount    = 0;
                        G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                    else
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= 25)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT97 == 1)
                                h_fig97 = figure(97);
                                h_fig97.Name = 'Start Bit Error';
                                x1 = G_PreStart:idx+100;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                xline(idx,'--', 'Current Positon');
                                title('Error Start Bit');
                            end
                            %----------------------------------------------------------

                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStartErrCnt = G_dStartErrCnt + 1;
                        end
                    end

                case RX_MDM_STATUS_PRELOAD
                    G_wRxBitCount = G_wRxBitCount + 1;
                    if (G_wRxBitCount == 8)
                        G_wRxBitCount = 0;
                        G_wCrcRegData = 0xffff;
                        G_wRxRunStatus= RX_MDM_STATUS_DATA;
                        %ClrRxRawFormTemp();

                        nTemp = bitshift(G_wRxShiftReg, 2);
                        nTemp = bitand(nTemp, 0x00ff);
                        nMsgID = G_vReverDataTableX(nTemp + 1);
                        if (nMsgID < 0 || nMsgID > 27)
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dPloadErrCnt = G_dPloadErrCnt + 1;
                        else
                            m_wRxMaxBitSize = (G_vMaxBitSize(nMsgID + 1) + 16 + 2);
                        end
                    end

                case RX_MDM_STATUS_DATA
                    if (bitand(G_wRxShiftReg, 0x3f00) ~= 0x3e00)      % It's not a stuffing bit
                        G_wRxBitCount = G_wRxBitCount + 1;
                        if(G_wRxBitCount >= m_wRxMaxBitSize+1)
                            %----------------------------------------------------------
                            if (ENABLE_PLOT98 == 1)
                                h_fig98 = figure(98);
                                h_fig98.Name = 'Stuffing Bit Error';
                                x1 = G_PreStart:idx;
                                x2 = G_PreStart:idx;
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x2, AdativeDcOffset(x2), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Error Stuffing Bit');
                            end
                            %----------------------------------------------------------

                            %%%%%% ResetToRxStatusPreamble()
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_bRxByteData = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_dStuffErrCnt = G_dStuffErrCnt + 1;
                            continue;
                        end

                        G_wNewBitData = bitand(bitshift(G_wRxShiftReg, -8), 0x0001);
                        G_bRxByteData = bitor(bitshift(G_bRxByteData, -1), bitand(bitshift(G_wRxShiftReg, -1), 0x0080));
                        G_BitDataArray(G_wRxBitCount) = G_wNewBitData;

                        % 신호 수집 (Viterbi 신호 기반 보정용)
                        if ENABLE_VITERBI_MLSD == 1
                            if (ENABLE_GMSK_RX_FLT > 0)
                                G_PacketSignal = [G_PacketSignal, G_pFilteredData(idx)];
                            else
                                G_PacketSignal = [G_PacketSignal, G_pSrcDataCh1(idx)];
                            end
                        end

                        G_wCrcRegData = update_crc(G_wCrcRegData, G_wNewBitData);
                    end

                    if (bitand(G_wRxShiftReg, 0x00ff) == 0x007e)
                    %if (bitand(G_wRxShiftReg, 0x007f) == 0x007e)


                        if(G_wCrcRegData == 0xf0b8)                                 % This should give a result of 0xF0B8
                            %----------------------------------------------------------
                            if (ENABLE_PLOT3 == 1)
                                h_fig3 = figure(3);
                                h_fig3.Name = 'Received Data(CRC OK)';
                                x1 = G_PreStart:idx+50;
                                if (G_PreStart <= SYNC_DETECT_OFFSET)
                                    x2 = G_PreStart:nCurSymbolIdx;
                                    x3 = G_PreStart:nCurSymbolIdx;
                                else
                                    x2 = G_PreStart:idx;
                                    x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                end
                                plot(x1, G_pSrcDataCh1(x1), '-x', x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid; 
                                title('Received AIS Packet');
                            end
                            %----------------------------------------------------------



                            if (G_PreStart > (215042-10) && G_PreStart < (215042+10))
                                break;
                            end

                            %WritePacketIntoRxRawBuff();
                            G_wRxRunStatus = RX_MDM_STATUS_PREAMBLE;
                            G_wRxShiftReg  = 0;
                            G_wRxReferValue= DC_MID_LEVEL;
                            G_bRxByteData = 0;
                            G_dRcvPktCnt = G_dRcvPktCnt + 1;
                        else
                            % CRC 오류 발생 - Viterbi MLSD로 패킷 수정 시도
                            viterbi_success = false;

                            if ENABLE_VITERBI_MLSD == 1
                                % 현재 패킷의 비트 데이터 수집 (G_BitDataArray에서)
                                if G_wRxBitCount >= 168  % 최소 AIS 데이터 길이
                                    packet_bits = G_BitDataArray(1:min(184, G_wRxBitCount));  % 최대 184비트

                                    fprintf('Viterbi CRC 수정 시도: 비트=%d개, 원본CRC=0x%04x\n', ...
                                        length(packet_bits), G_wCrcRegData);

                                    % 하이브리드 Viterbi MLSD: 실제 Viterbi + 비트 보정
                                    if length(G_PacketSignal) >= length(packet_bits)
                                        packet_signal = G_PacketSignal(1:length(packet_bits));

                                        fprintf('  하이브리드 Viterbi MLSD 시작...\n');
                                        [viterbi_bits, confidence] = viterbi_mlsd_decode_packet(packet_signal, G_ViterbiState);

                                        if ~isempty(viterbi_bits) && length(viterbi_bits) >= 168
                                            % Viterbi 결과로 CRC 계산
                                            test_crc = 0xffff;
                                            for k = 1:length(viterbi_bits)
                                                test_crc = update_crc(test_crc, viterbi_bits(k));
                                            end

                                            if test_crc == 0xf0b8
                                                success = true;
                                                corrected_bits = viterbi_bits;
                                                fprintf('  ✓ 순수 Viterbi MLSD 성공! (신뢰도: %.3f)\n', confidence);
                                            else
                                                fprintf('  → Viterbi 결과 CRC=0x%04x, 신뢰도=%.3f\n', test_crc, confidence);

                                                % Viterbi 결과를 기반으로 미세 조정
                                                [corrected_bits, success] = viterbi_fine_tune(viterbi_bits, packet_bits, confidence);

                                                if ~success
                                                    % 최종 백업: 기존 비트 플립 방식
                                                    fprintf('  → 기존 비트 플립 방식으로 전환\n');
                                                    [corrected_bits, success] = try_signal_based_correction(packet_bits, packet_signal);
                                                end
                                            end
                                        else
                                            fprintf('  ✗ Viterbi MLSD 디코딩 실패\n');
                                            [corrected_bits, success] = try_signal_based_correction(packet_bits, packet_signal);
                                        end
                                    else
                                        fprintf('  신호 데이터 부족, 기존 방식 사용\n');
                                        [corrected_bits, success] = try_signal_based_correction(packet_bits, []);
                                    end

                                    if success
                                        % try_bit_correction에서 이미 CRC=0xf0b8 검증 완료
                                        % Viterbi CRC 수정 성공!
                                        viterbi_success = true;
                                        G_ViterbiStats.corrected_packets = G_ViterbiStats.corrected_packets + 1;
                                        G_dRcvPktCnt = G_dRcvPktCnt + 1;  % 성공 패킷으로 카운트

                                        fprintf('✓ Viterbi CRC 수정 성공! 패킷 #%d (CRC: 0x%04x→0xf0b8)\n', ...
                                            G_ViterbiStats.corrected_packets, G_wCrcRegData);
                                    end
                                end
                            end

                            % Viterbi 수정 실패 시 기존 CRC 오류 처리
                            if ~viterbi_success
                                %----------------------------------------------------------
                                if (ENABLE_PLOT99 == 1)
                                    h_fig99 = figure(99);
                                    h_fig99.Name = 'Received Data(CRC ERROR)';
                                    x1 = G_PreStart:idx;
                                    if (G_PreStart <= SYNC_DETECT_OFFSET)
                                        x2 = G_PreStart:nCurSymbolIdx;
                                        x3 = G_PreStart:nCurSymbolIdx;
                                    else
                                        x2 = G_PreStart:idx;
                                        x3 = G_PreStart+SYNC_DETECT_OFFSET:nCurSymbolIdx;
                                    end
                                    plot(x2, G_pFilteredData(x2), '-o', x3, AdativeDcOffset(x3), '-m', x2, BitArray(x2), '-+'); grid;
                                    title('Received Error AIS Packet (Viterbi Failed)');
                                end
                                %----------------------------------------------------------

                                G_dCrcErrCnt = G_dCrcErrCnt + 1;
                                G_dCRCErrSymIdx(G_dCrcErrCnt) = G_PreStart;
                            end

                            %if (G_PreStart > (426187-10) && G_PreStart < (426187+10))
                            %    break;
                            %end

                            %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                            %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                            G_wRxBitCount    = 0;
                            %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                            G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                            G_bRxByteData    = 0;
                        end

                            %if (G_PreStart > (426187-10) && G_PreStart < (426187+10))
                            %    break;
                            %end

                            %m_dSampleCounter = cAisModem::GetSampleCounterValue();
                            %m_dSlotNoCounter = cAisModem::GetSlotNoCounterValue();

                            G_wRxBitCount    = 0;
                            %G_wRxRunStatus   = RX_MDM_STATUS_PRELOAD;
                            G_wRxRunStatus   = RX_MDM_STATUS_PREAMBLE;
                            G_bRxByteData    = 0;
                        end

                case RX_MDM_STATUS_PREAMBLE
                    % 프리앰블 상태 처리 (기존 코드 유지)
                    % 이 부분은 기존 로직을 그대로 유지

                otherwise
                    warning('Unexpected run status.');
            end
        end
        G_dStartSymbolIndex = nCurSymbolIdx+1;
    end
end

%-------------------------------------------------------------------------
% 결과 출력
fprintf('\n=== AIS 수신기 성능 결과 ===\n');
fprintf('동기 검출: %d개\n', G_dSyncDetCnt);
fprintf('ADC 오류: %d개\n', G_dAdcErrCnt);
fprintf('시작 비트 오류: %d개\n', G_dStartErrCnt);
fprintf('스터핑 비트 오류: %d개\n', G_dStuffErrCnt);
fprintf('CRC 오류: %d개\n', G_dCrcErrCnt);
fprintf('성공 패킷: %d개\n', G_dRcvPktCnt);

% Viterbi MLSD 통계 출력
if ENABLE_VITERBI_MLSD == 1
    fprintf('\n=== Viterbi MLSD CRC 수정 성능 ===\n');
    fprintf('Viterbi로 CRC 수정 성공: %d개\n', G_ViterbiStats.corrected_packets);
    if G_ViterbiStats.corrected_packets > 0
        fprintf('평균 신뢰도: %.3f\n', mean(G_ViterbiStats.confidence_history));
        fprintf('신뢰도 범위: %.3f ~ %.3f\n', min(G_ViterbiStats.confidence_history), max(G_ViterbiStats.confidence_history));

        % 원래 CRC 오류 개수 계산 (현재 CRC 오류 + 수정 성공)
        original_crc_errors = G_dCrcErrCnt + G_ViterbiStats.corrected_packets;
        fprintf('CRC 수정 성공률: %.1f%% (%d/%d)\n', ...
            (G_ViterbiStats.corrected_packets / original_crc_errors) * 100, ...
            G_ViterbiStats.corrected_packets, original_crc_errors);
        fprintf('CRC 오류 감소: %d개 → %d개 (%.1f%% 감소)\n', ...
            original_crc_errors, G_dCrcErrCnt, ...
            (G_ViterbiStats.corrected_packets / original_crc_errors) * 100);
    end
    fprintf('총 패킷 수신률: %d개 (기존 %d + Viterbi 수정 %d)\n', ...
        G_dRcvPktCnt, G_dRcvPktCnt - G_ViterbiStats.corrected_packets, G_ViterbiStats.corrected_packets);
    if G_ViterbiStats.corrected_packets > 0
        fprintf('수신률 향상: %.1f%% (기존 대비)\n', ...
            (G_ViterbiStats.corrected_packets / (G_dRcvPktCnt - G_ViterbiStats.corrected_packets)) * 100);
    end
end

figure(9);
bar_x = ["SyncDet" "AdcErr" "StartErr" "StuffErr" "CrcErr" "Packet OK" ];
bar_y = [G_dSyncDetCnt, G_dAdcErrCnt G_dStartErrCnt G_dStuffErrCnt G_dCrcErrCnt G_dRcvPktCnt];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840];
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')

% Viterbi 신뢰도 히스토리 플롯
if ENABLE_VITERBI_MLSD == 1 && ~isempty(G_ViterbiStats.confidence_history)
    figure(10);
    plot(G_ViterbiStats.confidence_history, 'bo-');
    title('Viterbi MLSD 신뢰도 히스토리');
    xlabel('패킷 번호');
    ylabel('신뢰도');
    grid on;

    figure(11);
    histogram(G_ViterbiStats.confidence_history, 20);
    title('Viterbi MLSD 신뢰도 분포');
    xlabel('신뢰도');
    ylabel('빈도');
    grid on;
end
%-------------------------------------------------------------------------

function [h0, bias] = calc_coefficient(received_data, source_data, half_impulse_response)
    INDEX_START = 1;

    %received_data = received_data - 0.1;
    A = [source_data(INDEX_START:length(received_data))', ones(length(received_data),1)];

    pinv_data = pinv(A);
    received_data = received_data';
    coeff_vector = pinv_data*received_data;
    h0 = coeff_vector(1)*max(half_impulse_response);
    bias = coeff_vector(2);

    figure(34)
    kkk = 1:length(received_data);
    scale = h0/max(half_impulse_response);
    subplot(4,1,1); plot(received_data); grid; title('received\_data');
    subplot(4,1,2); plot(source_data(INDEX_START:INDEX_START+length(received_data)-1)); grid; title('source\_data');
    subplot(4,1,3); plot(kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1), '-o', kkk, scale*source_data(INDEX_START:INDEX_START+length(received_data)-1)+bias, '-x', kkk, received_data, '-+'); grid; title('source\_data (o), received\_data (x)');
    subplot(4,1,4); plot(kkk, pinv_data(1,:), '-o', kkk, pinv_data(2,:), '-x'); grid; title('test');
end

%------------------------------------------------------------------------
% Viterbi 결과 미세 조정 (하이브리드 접근)
function [corrected_bits, success] = viterbi_fine_tune(viterbi_bits, original_bits, viterbi_confidence)
    success = false;
    corrected_bits = viterbi_bits;

    if length(viterbi_bits) < 168
        return;
    end

    fprintf('    Viterbi 미세 조정 시작 (신뢰도: %.3f)...\n', viterbi_confidence);

    % 1단계: Viterbi 결과에서 1-2비트만 수정
    for i = 1:min(100, length(viterbi_bits))
        test_bits = viterbi_bits;
        test_bits(i) = ~test_bits(i);

        test_crc = 0xffff;
        for k = 1:length(test_bits)
            test_crc = update_crc(test_crc, test_bits(k));
        end
        if test_crc == 0xf0b8
            corrected_bits = test_bits;
            success = true;
            fprintf('    ✓ Viterbi+1비트 수정 성공: 위치 %d\n', i);
            return;
        end
    end

    % 2단계: Viterbi와 원본 비트의 차이점 분석 후 선택적 수정
    if length(original_bits) >= length(viterbi_bits)
        compare_length = length(viterbi_bits);
        diff_positions = find(viterbi_bits ~= original_bits(1:compare_length));
        if length(diff_positions) <= 20  % 차이가 20비트 이하일 때만
            fprintf('    Viterbi와 원본 차이: %d비트\n', length(diff_positions));

            % 차이점 중 일부를 원본으로 되돌리기
            for num_revert = 1:min(5, length(diff_positions))
                if length(diff_positions) >= num_revert
                    for combo = nchoosek(diff_positions, num_revert)'
                        test_bits = viterbi_bits;
                        for pos = combo
                            if pos <= length(original_bits)
                                test_bits(pos) = original_bits(pos);
                            end
                        end

                        test_crc = 0xffff;
                        for k = 1:length(test_bits)
                            test_crc = update_crc(test_crc, test_bits(k));
                        end
                        if test_crc == 0xf0b8
                            corrected_bits = test_bits;
                            success = true;
                            fprintf('    ✓ Viterbi 하이브리드 수정 성공: %d비트 되돌림\n', num_revert);
                            return;
                        end
                    end
                end
            end
        end
    end

    % 3단계: 고신뢰도 Viterbi 결과에 대한 추가 보정
    if viterbi_confidence > 0.7
        for i = 1:min(50, length(viterbi_bits))
            for j = i+1:min(i+10, length(viterbi_bits))
                test_bits = viterbi_bits;
                test_bits(i) = ~test_bits(i);
                test_bits(j) = ~test_bits(j);

                test_crc = 0xffff;
                for k = 1:length(test_bits)
                    test_crc = update_crc(test_crc, test_bits(k));
                end
                if test_crc == 0xf0b8
                    corrected_bits = test_bits;
                    success = true;
                    fprintf('    ✓ 고신뢰도 Viterbi+2비트 수정 성공: 위치 %d, %d\n', i, j);
                    return;
                end
            end
        end
    end
end

%------------------------------------------------------------------------
% 신호 기반 비트 보정 (CRC 오류 수정용)
function [corrected_bits, success] = try_signal_based_correction(original_bits, signal_samples)
    % 신호가 있으면 신호 기반 보정, 없으면 기존 방식
    if ~isempty(signal_samples) && length(signal_samples) >= length(original_bits)
        [corrected_bits, success] = signal_based_bit_correction(original_bits, signal_samples);
        if success
            return;
        end
    end

    % 신호 기반 보정 실패 시 기존 비트 플립 방식
    [corrected_bits, success] = try_bit_correction(original_bits);
end

%------------------------------------------------------------------------
% 신호 기반 비트 보정
function [corrected_bits, success] = signal_based_bit_correction(original_bits, signal_samples)
    success = false;
    corrected_bits = original_bits;

    if length(original_bits) < 168 || length(signal_samples) < length(original_bits)
        return;
    end

    % 신호 분석
    dc_offset = mean(signal_samples);
    signal_std = std(signal_samples);
    threshold = dc_offset;

    % 신호 레벨 기반 비트 재결정
    corrected_bits = original_bits;
    for i = 1:length(original_bits)
        signal_level = signal_samples(i);
        expected_bit = (signal_level > threshold);

        % NRZI 디코딩 적용
        if i > 1
            prev_signal = signal_samples(i-1);
            prev_expected = (prev_signal > threshold);
            if expected_bit == prev_expected
                nrzi_bit = 0;  % 변화 없음
            else
                nrzi_bit = 1;  % 변화 있음
            end
        else
            nrzi_bit = expected_bit;
        end

        % 신호 레벨이 확실하고 기존 비트와 다르면 수정
        if abs(signal_level - threshold) > signal_std * 0.3
            corrected_bits(i) = nrzi_bit;
        end
    end

    % CRC 검증
    test_crc = 0xffff;
    for k = 1:length(corrected_bits)
        test_crc = update_crc(test_crc, corrected_bits(k));
    end
    if test_crc == 0xf0b8
        success = true;
        fprintf('  신호 기반 수정 성공\n');
        return;
    end

    % 신호 기반 + 1비트 추가 보정
    for i = 1:length(corrected_bits)
        test_bits = corrected_bits;
        test_bits(i) = ~test_bits(i);

        test_crc = 0xffff;
        for k = 1:length(test_bits)
            test_crc = update_crc(test_crc, test_bits(k));
        end
        if test_crc == 0xf0b8
            corrected_bits = test_bits;
            success = true;
            fprintf('  신호 기반 + 1비트 수정 성공: 위치 %d\n', i);
            return;
        end
    end
end

%------------------------------------------------------------------------
% 간단한 비트 보정 (CRC 오류 수정용) - 기존 방식
function [corrected_bits, success] = try_bit_correction(original_bits)
    % 1-3개 비트를 플립해서 CRC가 맞는지 확인
    success = false;
    corrected_bits = original_bits;

    if length(original_bits) < 168
        return;
    end

    % 전체 184비트 사용 (성공 패킷과 동일)
    test_bits = original_bits;

    % 1비트 오류 수정 시도 (전체 184비트에 대해)
    for i = 1:length(test_bits)
        modified_bits = test_bits;
        modified_bits(i) = ~modified_bits(i);  % 비트 플립

        % 기존 방식으로 CRC 계산 (전체 비트에 대해)
        test_crc = 0xffff;
        for k = 1:length(modified_bits)
            test_crc = update_crc(test_crc, modified_bits(k));
        end
        if test_crc == 0xf0b8
            corrected_bits = modified_bits;
            success = true;
            fprintf('  1비트 수정 성공: 위치 %d\n', i);
            return;
        end
    end

    % 2비트 오류 수정 시도 (빠른 버전)
    for i = 1:min(50, length(test_bits))  % 처음 50비트만
        for j = i+1:min(i+20, length(test_bits))  % 근처 20비트만
            modified_bits = test_bits;
            modified_bits(i) = ~modified_bits(i);
            modified_bits(j) = ~modified_bits(j);

            % 기존 방식으로 CRC 계산 (전체 비트에 대해)
            test_crc = 0xffff;
            for k = 1:length(modified_bits)
                test_crc = update_crc(test_crc, modified_bits(k));
            end
            if test_crc == 0xf0b8
                corrected_bits = modified_bits;
                success = true;
                fprintf('  2비트 수정 성공: 위치 %d, %d\n', i, j);
                return;
            end
        end
    end

    % 연속 2비트 플립 시도 (버스트 오류)
    for start_pos = 1:min(100, length(test_bits)-1)
        modified_bits = test_bits;
        modified_bits(start_pos) = ~modified_bits(start_pos);
        modified_bits(start_pos+1) = ~modified_bits(start_pos+1);

        % CRC 계산
        test_crc = 0xffff;
        for k = 1:length(modified_bits)
            test_crc = update_crc(test_crc, modified_bits(k));
        end
        if test_crc == 0xf0b8
            corrected_bits = modified_bits;
            success = true;
            fprintf('  연속 2비트 수정 성공: 위치 %d-%d\n', start_pos, start_pos+1);
            return;
        end
    end
end

%------------------------------------------------------------------------
% Viterbi MLSD 비트 보정 (기존 NRZI 결과 개선용) - 사용하지 않음
function [corrected_bits, confidence] = viterbi_correct_bits(original_bits, signal_samples, viterbi_state)
    % 기존 NRZI 디코딩 결과를 Viterbi로 보정

    if length(original_bits) < 168 || length(signal_samples) < length(original_bits)
        corrected_bits = [];
        confidence = 0;
        return;
    end

    % 신호 샘플을 비트 단위로 정규화
    if length(signal_samples) > length(original_bits)
        % 신호가 더 길면 비트 수에 맞게 다운샘플링
        downsample_ratio = floor(length(signal_samples) / length(original_bits));
        normalized_signal = zeros(1, length(original_bits));
        for i = 1:length(original_bits)
            start_idx = (i-1) * downsample_ratio + 1;
            end_idx = min(i * downsample_ratio, length(signal_samples));
            normalized_signal(i) = mean(signal_samples(start_idx:end_idx));
        end
    else
        normalized_signal = signal_samples(1:length(original_bits));
    end

    % 간단한 비트 보정: 신호 레벨과 기존 비트 비교
    corrected_bits = original_bits;
    correction_count = 0;

    % DC 오프셋 계산
    dc_offset = mean(normalized_signal);

    % 매우 보수적인 비트 보정: 매우 확실한 경우에만 수정
    signal_std = std(normalized_signal);
    threshold_margin = signal_std * 1.0;  % 신호 표준편차의 100%를 마진으로 사용 (더 엄격)

    for i = 1:length(original_bits)
        signal_level = normalized_signal(i);

        % 신호가 DC 오프셋에서 충분히 떨어져 있을 때만 보정 고려
        if abs(signal_level - dc_offset) > threshold_margin
            expected_bit = (signal_level > dc_offset);

            % NRZI 디코딩 고려 (이전 비트와의 관계)
            if i > 1
                if expected_bit == (normalized_signal(i-1) > dc_offset)
                    nrzi_bit = 0;  % 변화 없음
                else
                    nrzi_bit = 1;  % 변화 있음
                end
            else
                nrzi_bit = expected_bit;
            end

            % 기존 비트와 다르고 신호가 확실할 때만 보정
            if original_bits(i) ~= nrzi_bit
                corrected_bits(i) = nrzi_bit;
                correction_count = correction_count + 1;
            end
        end
    end

    % 개선된 신뢰도 계산
    if correction_count == 0
        confidence = 0.9;  % 보정이 필요없으면 높은 신뢰도
    else
        correction_ratio = correction_count / length(original_bits);
        if correction_ratio < 0.1  % 10% 미만 보정
            confidence = 0.8 - correction_ratio * 2;
        elseif correction_ratio < 0.2  % 20% 미만 보정
            confidence = 0.6 - correction_ratio * 1.5;
        else  % 20% 이상 보정 시 낮은 신뢰도
            confidence = max(0.05, 0.4 - correction_ratio);
        end
    end

    fprintf('  비트 보정: %d개 비트 수정 (%.1f%%)\n', correction_count, (correction_count/length(original_bits))*100);
end

%------------------------------------------------------------------------
% AIS 특화 Viterbi MLSD 디코딩 (실제 구현)
function [detected_bits, confidence] = viterbi_mlsd_decode_packet(signal, viterbi_state)
    N = length(signal);
    if N < 168
        detected_bits = [];
        confidence = 0;
        return;
    end

    % 신호 전처리
    signal = signal(:)';  % 행 벡터로 변환
    dc_offset = mean(signal);
    signal_std = std(signal);
    normalized_signal = (signal - dc_offset) / max(signal_std, 0.1);

    % 간단한 2-상태 Viterbi (NRZI 특성 활용)
    num_states = 2;  % 0: 이전 심볼과 같음, 1: 이전 심볼과 다름

    % 경로 메트릭 초기화
    path_metrics = [0, inf];  % 상태 0에서 시작
    survivor_paths = zeros(num_states, N);

    detected_bits = zeros(1, N);

    % Viterbi 디코딩
    for t = 1:N
        new_metrics = [inf, inf];
        new_paths = [0, 0];

        current_signal = normalized_signal(t);

        % 각 상태에서 각 입력 비트에 대한 메트릭 계산
        for prev_state = 1:num_states
            if path_metrics(prev_state) < inf
                for input_bit = 0:1
                    % NRZI: 0이면 변화없음, 1이면 변화
                    if input_bit == 0
                        next_state = prev_state;  % 상태 유지
                        if prev_state == 1
                            expected_level = 1;
                        else
                            expected_level = -1;
                        end
                    else
                        next_state = 3 - prev_state;  % 상태 변화 (1↔2)
                        if next_state == 1
                            expected_level = 1;
                        else
                            expected_level = -1;
                        end
                    end

                    % 브랜치 메트릭 (신호와 예상 레벨의 차이)
                    error = current_signal - expected_level;
                    branch_metric = error^2;

                    total_metric = path_metrics(prev_state) + branch_metric;

                    if total_metric < new_metrics(next_state)
                        new_metrics(next_state) = total_metric;
                        new_paths(next_state) = input_bit;
                        survivor_paths(next_state, t) = prev_state;
                    end
                end
            end
        end

        path_metrics = new_metrics;

        % 현재 시점에서 최적 비트 선택
        [~, best_state] = min(path_metrics);
        detected_bits(t) = new_paths(best_state);
    end

    % 신뢰도 계산
    final_metric = min(path_metrics);
    if final_metric < inf
        confidence = exp(-final_metric / (N * 2));  % 정규화된 신뢰도
        confidence = max(0.01, min(0.99, confidence));
    else
        confidence = 0.01;
    end

    % 184비트로 패딩 또는 자르기
    if length(detected_bits) > 184
        detected_bits = detected_bits(1:184);
    elseif length(detected_bits) < 184
        detected_bits = [detected_bits, zeros(1, 184-length(detected_bits))];
    end
end



%------------------------------------------------------------------------
% 비트 배열로부터 CRC 계산 (AIS 표준 방식)
function crc_result = calculate_crc_from_bits(bits)
    % AIS CRC 계산: 168비트 데이터에 대해서만 CRC 계산
    if length(bits) < 168
        crc_result = 0x0000;  % 데이터가 부족하면 0 반환
        return;
    end

    % CRC 초기값
    crc_result = 0xffff;

    % 168비트 데이터에 대해서만 CRC 계산 (CRC 비트 제외)
    data_bits = bits(1:168);

    for i = 1:length(data_bits)
        crc_result = update_crc(crc_result, data_bits(i));
    end

    % AIS CRC는 최종적으로 XOR 처리
    crc_result = bitxor(crc_result, 0xffff);
end